import json
from llama_index.core import Document, VectorStoreIndex
from llama_index.core.schema import TextNode
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from typing import List, Dict, Any
import uuid


def load_qdrant_data(json_file_path: str) -> Dict[str, Any]:
    """Load the Qdrant data from JSON file."""
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data


def convert_to_llamaindex_documents(qdrant_data: Dict[str, Any]) -> List[Document]:
    """Convert Qdrant data to LlamaIndex Documents."""
    documents = []
    
    # Extract points from the result
    points = qdrant_data.get('result', {}).get('points', [])
    
    for point in points:
        # Extract the payload data
        payload = point.get('payload', {})
        page_content = payload.get('page_content', '')
        metadata = payload.get('metadata', {})
        
        # Create a Document with the text content and metadata
        doc = Document(
            text=page_content,
            metadata={
                'id': point.get('id'),
                'name': metadata.get('name', ''),
                'code': metadata.get('code', ''),
                'source': 'qdrant_langchain_products'
            }
        )
        
        documents.append(doc)
    
    return documents


def convert_to_llamaindex_nodes_with_embeddings(qdrant_data: Dict[str, Any]) -> List[TextNode]:
    """Convert Qdrant data to LlamaIndex TextNodes with embeddings."""
    nodes = []
    
    # Extract points from the result
    points = qdrant_data.get('result', {}).get('points', [])
    
    for point in points:
        # Extract the payload data
        payload = point.get('payload', {})
        page_content = payload.get('page_content', '')
        metadata = payload.get('metadata', {})
        vector = point.get('vector', [])
        
        # Create a TextNode with the text content, metadata, and embedding
        node = TextNode(
            text=page_content,
            id_=point.get('id'),
            metadata={
                'name': metadata.get('name', ''),
                'code': metadata.get('code', ''),
                'source': 'qdrant_langchain_products'
            },
            embedding=vector if vector else None
        )
        
        nodes.append(node)
    
    return nodes


def save_documents_to_json(documents: List[Document], output_file: str):
    """Save LlamaIndex documents to JSON file."""
    docs_data = []
    for doc in documents:
        doc_dict = {
            'text': doc.text,
            'metadata': doc.metadata,
            'doc_id': doc.doc_id
        }
        docs_data.append(doc_dict)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(docs_data, f, indent=2, ensure_ascii=False)


def save_nodes_to_json(nodes: List[TextNode], output_file: str):
    """Save LlamaIndex TextNodes to JSON file."""
    nodes_data = []
    for node in nodes:
        node_dict = {
            'text': node.text,
            'metadata': node.metadata,
            'id_': node.id_,
            'embedding': node.embedding
        }
        nodes_data.append(node_dict)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(nodes_data, f, indent=2, ensure_ascii=False)


def create_qdrant_collection_with_llamaindex(nodes: List[TextNode], collection_name: str = "ag_products"):
    """Create a new Qdrant collection with LlamaIndex TextNodes."""
    # Setup Qdrant client (same as in retreival.py)
    sync_client = QdrantClient(
        host="*************",
        port=6333,
        api_key=None,
        timeout=10,
        https=False,
        prefer_grpc=False
    )
    # sync_client.create_collection(
    #     collection_name=collection_name,
    #     vectors_config={
    #         "size": 1536,  # OpenAI embedding dimensions
    #         "distance": "Cosine"  # Use cosine similarity
    #     }
    # )
    # Create QdrantVectorStore for the new collection
    ag_products_vector_store = QdrantVectorStore(
        client=sync_client,
        collection_name=collection_name,
        enable_hybrid=False,
    )
    ag_products_vector_store.add(nodes)
    # Create VectorStoreIndex from the nodes with existing embeddings
    print(f"Creating VectorStoreIndex for collection '{collection_name}'...")
    # ag_products_index = VectorStoreIndex(
    #     nodes=nodes,
    #     vector_store=ag_products_vector_store
    # )

    print(f"Successfully created collection '{collection_name}' with {len(nodes)} documents")


def main():
    """Main function to convert Qdrant data to LlamaIndex format and create new collection."""
    # Load the Qdrant data
    print("Loading Qdrant data...")
    qdrant_data = load_qdrant_data('langchain_products_data.json')

    # Convert to LlamaIndex TextNodes (with embeddings)
    print("Converting to LlamaIndex TextNodes with embeddings...")
    nodes = convert_to_llamaindex_nodes_with_embeddings(qdrant_data)
    print(f"Created {len(nodes)} nodes with embeddings")

    # Create new Qdrant collection with LlamaIndex
    print("\nCreating new Qdrant collection 'ag_products'...")
    try:
        ag_products_index = create_qdrant_collection_with_llamaindex(nodes, "ag_products")
        print("✓ Successfully created ag_products collection in Qdrant")
        print(f"✓ Collection contains {len(nodes)} documents with OpenAI embeddings (1536 dimensions)")
    except Exception as e:
        print(f"✗ Error creating collection: {e}")
        return

    print("\n🎉 ag_products collection created successfully!")
    print("You can now use this collection in your retrieval.py file.")


if __name__ == "__main__":
    main()
