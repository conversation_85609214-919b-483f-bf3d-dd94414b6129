from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent
from langgraph.store.memory import InMemoryStore
from langgraph.utils.config import get_store
from langmem import create_manage_memory_tool, create_memory_manager,ReflectionExecutor
from pydantic import BaseModel
from typing import Optional
from retreival import qna_search, product_search
# ✅ 1. Define user profile schema
class UserProfile(BaseModel):
    name: Optional[str] = None
    language: Optional[str] = None
    timezone: Optional[str] = None
    age: Optional[int] = None
    academic_level: Optional[str] = None
    interests: Optional[list[dict]] = None
    career_goals: Optional[list[dict]] = None

# ✅ 2. Memory extraction manager
manager = create_memory_manager(
    "openai:gpt-4.1-mini",
    schemas=[UserProfile],
    instructions="Extract user profile (interests, academic level, goals, etc.) from conversation.",
    enable_inserts=False,
)

# ✅ 3. Dummy retriever tool
async def retriever_tool(query: str) -> str:
    """Simulated retriever for courses"""
    return await qna_search(query)

# ✅ 4. Prompt that merges memories
def prompt(state):
    store = get_store()
    memories = store.search(("memories",), query=state["messages"][-1].content)

    system_msg = f"""
You are a helpful and knowledgeable AI assistant for a Nepali EdTech company.  


- Always use stored user profile info if available.
- If user asks for courses, check their interests first.
- If no relevant memory, retrieve general course suggestions.


Your main goals are to:  
- Guide students in Nepal about what subjects or courses to study based on their interests, strengths, and future career goals.  
- Provide career counseling with clear, practical advice about different professions, required qualifications, and growth opportunities.  
- Recommend online classes, skill-development programs, and higher education options.  
- Share information about scholarship opportunities, entrance exams, and study tips.  
- Explain complex topics in simple, easy-to-understand language.  


Tone and Style:  
- Be friendly, encouraging, and supportive.  
- Use simple English (with Nepali references if needed).  
- Keep answers concise but informative.  
- Always focus on what’s most relevant for students in Nepal. 
- Respond in English unless the user prefers Nepali. 

Your goal is to help students make better academic and career decisions while feeling motivated and confident about their future.


## Stored Memories:
{memories}
"""
    return [{"role": "system", "content": system_msg}, *state["messages"]]

# ✅ 5. Setup memory store & checkpoint
store = InMemoryStore(
    index={
        "dims": 1536,
        "embed": "openai:text-embedding-3-small",
    }
)
checkpointer = MemorySaver()

# ✅ 6. Create the agent with memory & retriever tools
agent = create_react_agent(
    "openai:gpt-4.1-mini",
    prompt=prompt,
    tools=[
        create_manage_memory_tool(namespace=("memories",)),
        retriever_tool,  # Q&A from retriever
    ],
    store=store,
    checkpointer=checkpointer,
)

# ✅ 7. Simple chat loop
if __name__ == "__main__":
    print("🎓 Nepali EdTech AI Assistant\n(Type 'exit' to quit)\n")
    new_config={"configurable": {"thread_id": "thread-b"}}
    while True:
        user_input = input("You: ")
        if user_input.lower() in ["exit", "quit"]:
            print("👋 Goodbye!")
            break

        # Send input to agent
        result = agent.invoke(
    {"messages": [{"role": "user", "content": user_input}]},
    config=new_config,
)
        
        # Print AI response
        ai_reply = result["messages"][-1].content
        print(f"AI: {ai_reply}\n")
